#!/usr/bin/env python3
"""
MCP 工具测试文件
"""

import asyncio
import json
from fastmcp import Client
from main import mcp


async def test_weather_tool():
    """测试天气工具"""
    print("🌤️  测试天气工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试获取北京天气
            result = await client.call_tool("get_weather", {
                "city": "Beijing",
                "units": "metric"
            })
            print(f"✅ 天气工具测试成功:")
            if hasattr(result, 'content') and result.content:
                print(f"   结果: {result.content[0].text}")
            else:
                print(f"   结果: {result}")
            
        except Exception as e:
            print(f"❌ 天气工具测试失败: {e}")


async def test_news_tool():
    """测试新闻工具"""
    print("\n📰 测试新闻工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试获取科技新闻
            result = await client.call_tool("get_news", {
                "category": "technology",
                "country": "us",
                "page_size": 5
            })
            print(f"✅ 新闻工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ 新闻工具测试失败: {e}")


async def test_exchange_rates_tool():
    """测试汇率工具"""
    print("\n💱 测试汇率工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试获取USD汇率
            result = await client.call_tool("get_exchange_rates", {
                "base_currency": "USD"
            })
            print(f"✅ 汇率工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ 汇率工具测试失败: {e}")


async def test_webpage_tool():
    """测试网页内容工具"""
    print("\n🌐 测试网页内容工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试获取网页内容
            result = await client.call_tool("get_webpage_content", {
                "url": "https://httpbin.org/json",
                "max_length": 1000
            })
            print(f"✅ 网页内容工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ 网页内容工具测试失败: {e}")


async def test_database_tool():
    """测试数据库查询工具"""
    print("\n🗄️  测试数据库查询工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试查询用户表
            result = await client.call_tool("query_database", {
                "table": "users",
                "query_type": "select",
                "limit": 5
            })
            print(f"✅ 数据库查询工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ 数据库查询工具测试失败: {e}")


async def test_system_info_tool():
    """测试系统信息工具"""
    print("\n💻 测试系统信息工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试获取基本系统信息
            result = await client.call_tool("get_system_info", {
                "info_type": "basic"
            })
            print(f"✅ 系统信息工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ 系统信息工具测试失败: {e}")


async def test_api_status_tool():
    """测试API状态检查工具"""
    print("\n🔍 测试API状态检查工具...")
    
    async with Client(mcp) as client:
        try:
            # 测试检查API状态
            result = await client.call_tool("check_api_status", {
                "url": "https://httpbin.org/status/200",
                "timeout": 10
            })
            print(f"✅ API状态检查工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ API状态检查工具测试失败: {e}")


async def test_file_read_tool():
    """测试文件读取工具"""
    print("\n📄 测试文件读取工具...")
    
    # 首先创建一个测试文件
    test_file_path = "test_data.txt"
    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write("这是一个测试文件\n包含一些示例数据\n用于测试文件读取功能")
    
    async with Client(mcp) as client:
        try:
            # 测试读取文件
            result = await client.call_tool("read_file", {
                "file_path": test_file_path,
                "encoding": "utf-8"
            })
            print(f"✅ 文件读取工具测试成功:")
            print(f"   结果: {result.text[:200]}...")
            
        except Exception as e:
            print(f"❌ 文件读取工具测试失败: {e}")
        finally:
            # 清理测试文件
            import os
            if os.path.exists(test_file_path):
                os.remove(test_file_path)


async def list_available_tools():
    """列出所有可用工具"""
    print("\n🔧 可用工具列表:")
    
    async with Client(mcp) as client:
        tools = await client.list_tools()
        for i, tool in enumerate(tools.tools, 1):
            print(f"   {i}. {tool.name}: {tool.description}")


async def main():
    """运行所有测试"""
    print("🚀 开始测试 MCP 数据获取工具...")
    
    # 列出可用工具
    await list_available_tools()
    
    # 运行各个工具测试
    test_functions = [
        test_weather_tool,
        test_news_tool,
        test_exchange_rates_tool,
        test_webpage_tool,
        test_database_tool,
        test_system_info_tool,
        test_api_status_tool,
        test_file_read_tool,
    ]
    
    for test_func in test_functions:
        try:
            await test_func()
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 时发生错误: {e}")
    
    print("\n✨ 测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
