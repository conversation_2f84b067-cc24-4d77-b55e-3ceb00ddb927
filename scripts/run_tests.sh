#!/bin/bash

# MCP 数据源工具测试脚本

echo "🧪 运行 MCP 数据源工具测试..."

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 检查虚拟环境是否存在
if [ ! -d ".venv" ]; then
    echo "❌ 未找到 .venv 目录，请先创建虚拟环境"
    echo "   运行: python3 -m venv .venv"
    exit 1
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source .venv/bin/activate

# 安装依赖
echo "📥 确保依赖已安装..."
pip install -r requirements.txt > /dev/null 2>&1

echo ""
echo "=" * 60
echo "🚀 运行快速测试..."
echo "=" * 60
python tests/quick_test.py

echo ""
echo "=" * 60
echo "🔍 运行完整测试..."
echo "=" * 60
python tests/test_data_tools.py

echo ""
echo "✨ 测试完成！"
