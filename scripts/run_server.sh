#!/bin/bash

# MCP 数据源工具服务器启动脚本

echo "🚀 启动 MCP 数据源工具服务器..."

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 检查虚拟环境是否存在
if [ ! -d ".venv" ]; then
    echo "❌ 未找到 .venv 目录，请先创建虚拟环境"
    echo "   运行: python3 -m venv .venv"
    exit 1
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source .venv/bin/activate

# 安装依赖
echo "📥 安装依赖..."
pip install -r requirements.txt

# 启动服务器
echo "🌟 启动 MCP 服务器..."
python src/main.py
