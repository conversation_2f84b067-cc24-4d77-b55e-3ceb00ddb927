#!/usr/bin/env python3
"""
MCP 数据源工具服务器
基于 data 目录下的 .md 文件动态创建数据查询工具
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastmcp import FastMCP
from src.core.data_manager import DataSourceManager
from src.core.query_service import QueryService
from src.tools.tool_generator import ToolGenerator


def create_mcp_server():
    """创建 MCP 服务器"""
    # 创建 FastMCP 服务器实例
    mcp = FastMCP(name="DataSourceTools")
    
    # 初始化数据源管理器
    data_manager = DataSourceManager()
    
    # 创建查询服务
    query_service = QueryService(data_manager)
    
    # 创建工具生成器
    tool_generator = ToolGenerator(mcp, query_service)
    
    # 生成所有工具
    tool_generator.generate_all_tools(data_manager.get_all_data_sources())
    
    return mcp


def main():
    """主函数"""
    try:
        # 创建并启动 MCP 服务器
        mcp_server = create_mcp_server()
        
        print("🚀 MCP 数据源工具服务器启动成功")
        print("📊 服务器正在运行，等待客户端连接...")
        
        # 运行服务器
        mcp_server.run()
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
