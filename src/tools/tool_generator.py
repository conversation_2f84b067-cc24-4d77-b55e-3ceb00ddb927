"""
MCP 工具生成器
动态创建数据源查询工具
"""

from typing import Dict, Any, Optional
from typing import Annotated
from pydantic import Field
from fastmcp import FastMCP, Context
from fastmcp.exceptions import ToolError


class ToolGenerator:
    """MCP 工具生成器"""
    
    def __init__(self, mcp_server: FastMCP, query_service):
        self.mcp_server = mcp_server
        self.query_service = query_service
    
    def create_data_query_tool(self, source_name: str, source_info: Dict):
        """为数据源创建查询工具"""
        
        @self.mcp_server.tool(
            name=f"query_{source_name}",
            description=f"查询{source_info['title']}数据",
            annotations={
                "title": f"{source_info['title']}查询工具",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        async def query_data_source(
            limit: Annotated[int, Field(description="返回记录数量限制", ge=1, le=100)] = 10,
            enterprise_name: Annotated[Optional[str], Field(description="企业名称过滤")] = None,
            ctx: Context = None
        ) -> Dict[str, Any]:
            """查询数据源"""
            
            if ctx:
                await ctx.info(f"正在查询{source_info['title']}数据...")
            
            try:
                result = self.query_service.query_data_source(
                    source_name=source_name,
                    limit=limit,
                    enterprise_name=enterprise_name
                )
                
                if ctx:
                    await ctx.info(f"成功返回 {result['returned_records']} 条{source_info['title']}记录")
                
                return result
                
            except Exception as e:
                if ctx:
                    await ctx.error(f"查询{source_info['title']}失败: {str(e)}")
                raise ToolError(f"查询{source_info['title']}失败: {str(e)}")
        
        return query_data_source
    
    def create_management_tools(self):
        """创建管理工具"""
        
        @self.mcp_server.tool(
            description="列出所有可用的数据源",
            annotations={
                "title": "数据源列表工具",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        async def list_data_sources(ctx: Context = None) -> Dict[str, Any]:
            """列出所有可用的数据源"""
            
            if ctx:
                await ctx.info("正在获取数据源列表...")
            
            try:
                result = self.query_service.list_all_data_sources()
                
                if ctx:
                    await ctx.info(f"找到 {result['total_sources']} 个数据源")
                
                return result
                
            except Exception as e:
                if ctx:
                    await ctx.error(f"获取数据源列表失败: {str(e)}")
                raise ToolError(f"获取数据源列表失败: {str(e)}")
        
        @self.mcp_server.tool(
            description="获取指定数据源的详细信息和字段说明",
            annotations={
                "title": "数据源信息工具",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        async def get_data_source_info(
            source_name: Annotated[str, Field(description="数据源名称")],
            ctx: Context = None
        ) -> Dict[str, Any]:
            """获取数据源详细信息"""
            
            if ctx:
                await ctx.info(f"正在获取数据源 {source_name} 的信息...")
            
            try:
                result = self.query_service.get_data_source_info(source_name)
                
                if ctx:
                    await ctx.info(f"成功获取数据源 {source_name} 的信息")
                
                return result
                
            except ValueError as e:
                if ctx:
                    await ctx.error(str(e))
                raise ToolError(str(e))
            except Exception as e:
                if ctx:
                    await ctx.error(f"获取数据源信息失败: {str(e)}")
                raise ToolError(f"获取数据源信息失败: {str(e)}")
        
        @self.mcp_server.tool(
            description="跨数据源搜索关键词",
            annotations={
                "title": "跨源搜索工具",
                "readOnlyHint": True,
                "openWorldHint": False
            }
        )
        async def search_across_sources(
            keyword: Annotated[str, Field(description="搜索关键词")],
            limit_per_source: Annotated[int, Field(description="每个数据源返回的最大记录数", ge=1, le=20)] = 5,
            ctx: Context = None
        ) -> Dict[str, Any]:
            """跨数据源搜索"""
            
            if ctx:
                await ctx.info(f"正在跨数据源搜索关键词: {keyword}")
            
            try:
                result = self.query_service.search_across_sources(keyword, limit_per_source)
                
                if ctx:
                    await ctx.info(f"搜索完成，找到 {result['total_matches']} 条匹配记录")
                
                return result
                
            except Exception as e:
                if ctx:
                    await ctx.error(f"跨源搜索失败: {str(e)}")
                raise ToolError(f"跨源搜索失败: {str(e)}")
    
    def generate_all_tools(self, data_sources: Dict[str, Dict]):
        """生成所有工具"""
        # 创建管理工具
        self.create_management_tools()
        
        # 为每个数据源创建查询工具
        for source_name, source_info in data_sources.items():
            self.create_data_query_tool(source_name, source_info)
