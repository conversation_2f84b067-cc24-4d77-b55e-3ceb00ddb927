# server.py
import httpx
from mcp.server.fastmcp import FastMCP
import io
import os
import sys
import json

from typing import List, Optional
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def load_spwork_activities(file_):
    with open(file_, 'r') as f:
        data = json.load(f)
    return data

def load_risk_level(file_):
    with open(file_, 'r') as f:
        data = json.load(f)
    return data



@mcp.tool()
async def fetch_spwork_activities(work_date: str, enterprise_name: Optional[str] = None) -> List[str]:
    """
    根据指定的日期和企业名字全称（可选参数）获取完成的或者将要进行的特殊作业活动信息
    :param work_date: 作业日期
    :param enterprise_name: 企业名字全称
    :return :返回指定日期的特殊作业活动信息
    """
    ;



@mcp.tool()
async def fetch_spwork_area_risk(enterprise_name: str, area_name: str) -> str:
    """
    根据企业名字全称和区域名称，获取该区域的风险等级
    :param enterprise_name: 企业名字全称
    :param area_name: 区域名称
    :return :返回该区域的风险等级信息
    """

@mcp.tool()
async def fetch_spwork_area_alarm_risk(enterprise_name: str, area_name: str, time: str) -> str:
    """
    根据企业名字全称、区域名称、时间来获取该区域的传感器报警信息，根据区域的传感器报警信息评估周边环境的风险等级
    :param enterprise_name: 企业名字全称
    :param area_name: 区域名称
    :param time: 日期
    :return :返回该区域周边环境的风险信息
    """

@mcp.tool()
async def fetch_spwork_area_whereis_majority_risk(enterprise_name: str, area: str) -> str:
    """
    根据企业名字全称、区域名称获取该区域是否属于重大危险源管控区域
    :param enterprise_name: 企业名字全称
    :param area: 区域名称
    :return :返回该区域是否是重大危险源
    """



@mcp.tool()
async def fetch_spwork_wheris_in_openparking_status(enterprise_name: str, area: str, time: str) -> str:
    """
    根据企业名字全称、区域名称、时间获取该作业时间是否处于周边设备的开停车状态内，根据周边设备的开停车状态评估该区域的危险等级
    :param enterprise_name： 企业名字全称
    :param area: 区域名称
    :param time: 时间
    :return 根据周边设备的开停车状态评估该区域的危险等级
    """



