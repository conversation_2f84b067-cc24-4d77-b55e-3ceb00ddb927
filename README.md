# MCP 数据源工具集合

这是一个基于 FastMCP 实现的数据源查询工具集合，自动从 `data` 目录下的 `.md` 文件创建对应的 MCP 工具，可以与支持 MCP (Model Context Protocol) 的 AI 应用程序集成。

> 📖 **详细文档请查看**: [docs/README.md](docs/README.md)
> 📊 **项目重构总结**: [docs/PROJECT_SUMMARY.md](docs/PROJECT_SUMMARY.md)

## 🚀 快速开始

### 启动服务器
```bash
./scripts/run_server.sh
```

### 运行测试
```bash
./scripts/run_tests.sh
```

## 📊 当前数据源

- **告警记录** (10条) - DCS报警信息
- **区域信息** (4条) - 企业区域管理信息
- **开停车信息** (8条) - 设备开停车状态
- **重大危险源** (1条) - 重大危险源管控信息
- **作业申报信息** (19条) - 各类作业申报数据
- **风险分级** (7条) - 风险等级管控数据

**总计**: 6个数据源，49条记录，9个动态工具

## 📁 项目结构

```
mcp-tools/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   ├── tools/             # 工具生成器
│   └── main.py            # 主服务器
├── tests/                 # 测试文件
├── scripts/               # 启动脚本
├── docs/                  # 详细文档
├── data/                  # 数据源文件
└── requirements.txt       # 依赖列表
```

## 🔧 扩展数据源

要添加新的数据源，只需：

1. 在 `data` 目录下创建新的 `.md` 文件
2. 按照指定格式添加数据和字段映射
3. 重启服务器，系统会自动创建对应的查询工具

## 🔗 相关链接

- [FastMCP 文档](https://gofastmcp.com)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [Claude Desktop](https://claude.ai/desktop)
