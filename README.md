# MCP 数据源工具集合

这是一个基于 FastMCP 实现的数据源查询工具集合，自动从 `data` 目录下的 `.md` 文件创建对应的 MCP 工具，可以与支持 MCP (Model Context Protocol) 的 AI 应用程序集成。

## 🚀 功能特性

### 📊 动态数据源工具

系统会自动扫描 `data` 目录下的所有 `.md` 文件，为每个数据源创建对应的查询工具：

1. **告警记录查询工具** (`query_alarm_record`)
   - 查询DCS报警信息数据
   - 支持企业名称过滤
   - 包含报警内容、区域、时间等信息

2. **区域信息查询工具** (`query_area_info`)
   - 查询区域信息数据
   - 包含区域名称、类型、面积、负责人等
   - 支持重大危险源标识

3. **开停车信息查询工具** (`query_open_parking`)
   - 查询开停车信息数据
   - 包含责任人、操作类型、当前状态等
   - 支持联锁状态查询

4. **重大危险源查询工具** (`query_major_hazard_sources`)
   - 查询重大危险源信息数据
   - 包含危险源详细信息和管控措施

5. **作业申报信息查询工具** (`query_job_reporting_info`)
   - 查询作业申报信息数据
   - 包含作业类型、时间、负责人等信息

6. **风险分级查询工具** (`query_risk_level`)
   - 查询风险分级数据
   - 包含管控对象、风险等级、责任部门等

### 🛠️ 通用管理工具

7. **数据源列表工具** (`list_data_sources`)
   - 列出所有可用的数据源
   - 显示记录数量和字段映射
   - 提供工具名称对照

8. **数据源信息工具** (`get_data_source_info`)
   - 获取指定数据源的详细信息
   - 显示字段说明和样本数据
   - 支持中英文字段对照

## 📦 安装

1. **克隆项目**
```bash
git clone <repository-url>
cd mcp-tools
```

2. **创建虚拟环境**
```bash
python3 -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **准备数据源**
   - 确保 `data` 目录下有 `.md` 格式的数据文件
   - 每个 `.md` 文件将自动创建对应的查询工具

## 🔧 使用方法

### 启动 MCP 服务器

```bash
# 使用启动脚本（推荐）
./run_server.sh

# 或直接运行
source .venv/bin/activate
python main.py
```

服务器将在标准输入/输出模式下运行，等待 MCP 客户端连接。

### 运行测试

```bash
# 测试数据源工具
python test_data_tools.py

# 快速验证
python quick_test.py
```

这将运行所有数据源工具的测试用例，验证功能是否正常。

### 与 AI 应用集成

#### Claude Desktop 配置

在 Claude Desktop 的配置文件中添加：

```json
{
  "mcpServers": {
    "data-fetcher": {
      "command": "python",
      "args": ["/path/to/your/main.py"],
      "cwd": "/path/to/your/project"
    }
  }
}
```

#### 其他 MCP 客户端

可以通过标准的 MCP 协议连接到服务器，使用 stdio 传输方式。

## 🛠️ 工具详细说明

### 天气查询工具

```python
# 获取北京的天气信息
result = await client.call_tool("get_weather", {
    "city": "Beijing",
    "units": "metric"  # 可选: metric, imperial, kelvin
})
```

### 新闻获取工具

```python
# 获取科技类新闻
result = await client.call_tool("get_news", {
    "category": "technology",
    "country": "us",
    "page_size": 10
})

# 搜索特定主题
result = await client.call_tool("get_news", {
    "query": "artificial intelligence",
    "page_size": 5
})
```

### 汇率查询工具

```python
# 获取以美元为基准的汇率
result = await client.call_tool("get_exchange_rates", {
    "base_currency": "USD"
})
```

### 网页内容获取工具

```python
# 获取网页内容
result = await client.call_tool("get_webpage_content", {
    "url": "https://example.com",
    "max_length": 5000
})
```

### 文件读取工具

```python
# 读取本地文件
result = await client.call_tool("read_file", {
    "file_path": "data.txt",
    "encoding": "utf-8"
})
```

### 数据库查询工具

```python
# 查询用户表
result = await client.call_tool("query_database", {
    "table": "users",
    "query_type": "select",
    "limit": 10
})
```

### 系统信息工具

```python
# 获取系统基本信息
result = await client.call_tool("get_system_info", {
    "info_type": "basic"  # 可选: basic, disk, memory, network
})
```

### API状态检查工具

```python
# 检查API状态
result = await client.call_tool("check_api_status", {
    "url": "https://api.example.com/health",
    "timeout": 10
})
```

## 🔒 安全特性

- **路径验证**: 文件读取工具包含路径遍历攻击防护
- **输入验证**: 所有工具都包含输入参数验证
- **错误处理**: 完善的错误处理和用户友好的错误消息
- **超时控制**: 网络请求包含超时保护
- **编码安全**: 自动处理字符编码问题

## 📁 项目结构

```
mcp-tools/
├── main.py              # 主要的MCP服务器文件
├── config.py            # 配置文件
├── requirements.txt     # Python依赖
├── test_tools.py        # 测试文件
├── README.md           # 项目文档
└── utils/              # 工具模块
    ├── __init__.py
    ├── data_fetchers.py # 数据获取函数
    └── validators.py    # 数据验证函数
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License

## 🔗 相关链接

- [FastMCP 文档](https://gofastmcp.com)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [Claude Desktop](https://claude.ai/desktop)
