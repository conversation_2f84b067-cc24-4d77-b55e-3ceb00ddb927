#!/usr/bin/env python3
"""
快速测试脚本 - 验证 MCP 服务器是否能正常启动
"""

import asyncio
import sys
import traceback
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from fastmcp import Client
    from src.main import create_mcp_server
    print("✅ 成功导入 FastMCP 和主模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


async def test_server_startup():
    """测试服务器是否能正常启动"""
    try:
        print("🚀 测试服务器启动...")
        
        # 创建服务器
        mcp_server = create_mcp_server()
        
        # 尝试连接到服务器
        async with Client(mcp_server) as client:
            print("✅ 服务器连接成功")
            
            # 列出可用工具
            tools_response = await client.list_tools()
            tools = tools_response.tools if hasattr(tools_response, 'tools') else tools_response
            print(f"✅ 发现 {len(tools)} 个工具:")
            
            for i, tool in enumerate(tools, 1):
                print(f"   {i}. {tool.name}")
            
            return True
            
    except Exception as e:
        print(f"❌ 服务器启动测试失败: {e}")
        traceback.print_exc()
        return False


async def test_simple_tool():
    """测试一个简单的工具"""
    try:
        print("\n🔧 测试数据源列表工具...")
        
        mcp_server = create_mcp_server()
        async with Client(mcp_server) as client:
            result = await client.call_tool("list_data_sources", {})
            
            print("✅ 数据源列表工具测试成功")
            # 检查结果结构
            if hasattr(result, 'content') and result.content:
                content = result.content[0]
                if hasattr(content, 'text'):
                    print(f"   结果长度: {len(content.text)} 字符")
                else:
                    print(f"   结果类型: {type(content)}")
            else:
                print(f"   结果对象: {result}")
                print(f"   结果属性: {dir(result)}")
            
            return True
            
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        traceback.print_exc()
        return False


async def main():
    """运行快速测试"""
    print("🧪 MCP 数据源工具快速测试")
    print("=" * 50)
    
    # 测试服务器启动
    startup_ok = await test_server_startup()
    
    if startup_ok:
        # 测试简单工具
        tool_ok = await test_simple_tool()
        
        if tool_ok:
            print("\n🎉 所有测试通过！服务器运行正常。")
            print("\n📝 下一步:")
            print("   1. 运行 'python tests/test_data_tools.py' 进行完整测试")
            print("   2. 运行 'python src/main.py' 启动 MCP 服务器")
        else:
            print("\n⚠️  服务器启动成功，但工具测试失败")
    else:
        print("\n❌ 服务器启动失败")
        print("\n🔧 故障排除:")
        print("   1. 检查是否安装了所有依赖")
        print("   2. 检查 Python 版本是否 >= 3.10")
        print("   3. 查看错误信息进行调试")


if __name__ == "__main__":
    asyncio.run(main())
