#!/usr/bin/env python3
"""
数据源工具测试文件
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastmcp import Client
from src.main import create_mcp_server


def extract_result_text(result):
    """提取工具调用结果的文本内容"""
    if hasattr(result, 'content') and result.content:
        return result.content[0].text
    return str(result)


async def test_list_data_sources():
    """测试数据源列表工具"""
    print("📋 测试数据源列表工具...")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        try:
            result = await client.call_tool("list_data_sources", {})
            result_text = extract_result_text(result)
            print(f"✅ 数据源列表工具测试成功:")
            print(f"   结果: {result_text[:300]}...")
            
        except Exception as e:
            print(f"❌ 数据源列表工具测试失败: {e}")


async def test_get_data_source_info():
    """测试数据源信息工具"""
    print("\n📊 测试数据源信息工具...")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        try:
            result = await client.call_tool("get_data_source_info", {
                "source_name": "alarm_record"
            })
            result_text = extract_result_text(result)
            print(f"✅ 数据源信息工具测试成功:")
            print(f"   结果: {result_text[:300]}...")
            
        except Exception as e:
            print(f"❌ 数据源信息工具测试失败: {e}")


async def test_query_alarm_record():
    """测试查询告警记录工具"""
    print("\n🚨 测试查询告警记录工具...")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        try:
            result = await client.call_tool("query_alarm_record", {
                "limit": 5
            })
            result_text = extract_result_text(result)
            print(f"✅ 告警记录查询工具测试成功:")
            print(f"   结果: {result_text[:300]}...")
            
        except Exception as e:
            print(f"❌ 告警记录查询工具测试失败: {e}")


async def test_query_area_info():
    """测试查询区域信息工具"""
    print("\n🏢 测试查询区域信息工具...")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        try:
            result = await client.call_tool("query_area_info", {
                "limit": 3
            })
            result_text = extract_result_text(result)
            print(f"✅ 区域信息查询工具测试成功:")
            print(f"   结果: {result_text[:300]}...")
            
        except Exception as e:
            print(f"❌ 区域信息查询工具测试失败: {e}")


async def test_search_across_sources():
    """测试跨源搜索工具"""
    print("\n🔍 测试跨源搜索工具...")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        try:
            result = await client.call_tool("search_across_sources", {
                "keyword": "公司",
                "limit_per_source": 3
            })
            result_text = extract_result_text(result)
            print(f"✅ 跨源搜索工具测试成功:")
            print(f"   结果: {result_text[:300]}...")
            
        except Exception as e:
            print(f"❌ 跨源搜索工具测试失败: {e}")


async def test_query_with_enterprise_filter():
    """测试带企业名称过滤的查询"""
    print("\n🔍 测试企业名称过滤查询...")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        try:
            result = await client.call_tool("query_alarm_record", {
                "limit": 10,
                "enterprise_name": "宏远新材料有限公司"
            })
            result_text = extract_result_text(result)
            print(f"✅ 企业名称过滤查询测试成功:")
            print(f"   结果: {result_text[:300]}...")
            
        except Exception as e:
            print(f"❌ 企业名称过滤查询测试失败: {e}")


async def list_available_tools():
    """列出所有可用工具"""
    print("\n🔧 可用工具列表:")
    
    mcp_server = create_mcp_server()
    async with Client(mcp_server) as client:
        tools_response = await client.list_tools()
        tools = tools_response.tools if hasattr(tools_response, 'tools') else tools_response
        for i, tool in enumerate(tools, 1):
            print(f"   {i}. {tool.name}: {tool.description}")


async def main():
    """运行所有测试"""
    print("🚀 开始测试基于数据源的 MCP 工具...")
    
    # 列出可用工具
    await list_available_tools()
    
    # 运行各个工具测试
    test_functions = [
        test_list_data_sources,
        test_get_data_source_info,
        test_query_alarm_record,
        test_query_area_info,
        test_search_across_sources,
        test_query_with_enterprise_filter,
    ]
    
    for test_func in test_functions:
        try:
            await test_func()
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 时发生错误: {e}")
    
    print("\n✨ 测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
