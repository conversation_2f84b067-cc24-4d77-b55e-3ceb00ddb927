# MCP 数据源工具集合

这是一个基于 FastMCP 实现的数据源查询工具集合，自动从 `data` 目录下的 `.md` 文件创建对应的 MCP 工具，可以与支持 MCP (Model Context Protocol) 的 AI 应用程序集成。

## 🚀 功能特性

### 📊 动态数据源工具

系统会自动扫描 `data` 目录下的所有 `.md` 文件，为每个数据源创建对应的查询工具：

1. **告警记录查询工具** (`query_alarm_record`)
   - 查询DCS报警信息数据
   - 支持企业名称过滤
   - 包含报警内容、区域、时间等信息

2. **区域信息查询工具** (`query_area_info`)
   - 查询区域信息数据
   - 包含区域名称、类型、面积、负责人等
   - 支持重大危险源标识

3. **开停车信息查询工具** (`query_open_parking`)
   - 查询开停车信息数据
   - 包含责任人、操作类型、当前状态等
   - 支持联锁状态查询

4. **重大危险源查询工具** (`query_major_hazard_sources`)
   - 查询重大危险源信息数据
   - 包含危险源详细信息和管控措施

5. **作业申报信息查询工具** (`query_job_reporting_info`)
   - 查询作业申报信息数据
   - 包含作业类型、时间、负责人等信息

6. **风险分级查询工具** (`query_risk_level`)
   - 查询风险分级数据
   - 包含管控对象、风险等级、责任部门等

### 🛠️ 通用管理工具

7. **数据源列表工具** (`list_data_sources`)
   - 列出所有可用的数据源
   - 显示记录数量和字段映射
   - 提供工具名称对照

8. **数据源信息工具** (`get_data_source_info`)
   - 获取指定数据源的详细信息
   - 显示字段说明和样本数据
   - 支持中英文字段对照

9. **跨源搜索工具** (`search_across_sources`)
   - 在所有数据源中搜索关键词
   - 返回匹配的记录和统计信息
   - 支持每个数据源的结果数量限制

## 📦 安装

1. **克隆项目**
```bash
git clone <repository-url>
cd mcp-tools
```

2. **创建虚拟环境**
```bash
python3 -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **准备数据源**
   - 确保 `data` 目录下有 `.md` 格式的数据文件
   - 每个 `.md` 文件将自动创建对应的查询工具

## 🔧 使用方法

### 启动 MCP 服务器

```bash
# 使用启动脚本（推荐）
./scripts/run_server.sh

# 或直接运行
source .venv/bin/activate
python src/main.py
```

服务器将在标准输入/输出模式下运行，等待 MCP 客户端连接。

### 运行测试

```bash
# 运行所有测试
./scripts/run_tests.sh

# 或分别运行
python tests/quick_test.py      # 快速验证
python tests/test_data_tools.py # 完整测试
```

这将运行所有数据源工具的测试用例，验证功能是否正常。

## 📁 项目结构

```
mcp-tools/
├── src/                        # 源代码目录
│   ├── core/                   # 核心模块
│   │   ├── data_manager.py     # 数据源管理器
│   │   └── query_service.py    # 查询服务
│   ├── tools/                  # 工具模块
│   │   └── tool_generator.py   # 工具生成器
│   └── main.py                 # 主服务器文件
├── tests/                      # 测试目录
│   ├── test_data_tools.py      # 完整测试
│   └── quick_test.py           # 快速测试
├── scripts/                    # 脚本目录
│   ├── run_server.sh           # 启动脚本
│   └── run_tests.sh            # 测试脚本
├── docs/                       # 文档目录
│   └── README.md               # 项目文档
├── data/                       # 数据源目录
│   ├── alarm_record.md         # 告警记录数据
│   ├── area_info.md            # 区域信息数据
│   ├── open_parking.md         # 开停车信息数据
│   ├── major_hazard_sources.md # 重大危险源数据
│   ├── job_reporting_info.md   # 作业申报信息数据
│   └── risk_level.md           # 风险分级数据
├── requirements.txt            # Python依赖
└── .venv/                      # 虚拟环境目录
```

## 🛠️ 工具详细说明

### 数据源查询工具

每个数据源都有对应的查询工具，支持以下参数：

```python
# 查询告警记录（示例）
result = await client.call_tool("query_alarm_record", {
    "limit": 10,                    # 返回记录数量限制
    "enterprise_name": "某某公司"    # 可选：企业名称过滤
})
```

### 管理工具

```python
# 列出所有数据源
result = await client.call_tool("list_data_sources", {})

# 获取特定数据源信息
result = await client.call_tool("get_data_source_info", {
    "source_name": "alarm_record"
})

# 跨源搜索
result = await client.call_tool("search_across_sources", {
    "keyword": "公司",
    "limit_per_source": 5
})
```

### 返回数据格式

所有查询工具返回统一格式的数据：

```json
{
    "data_source": "数据源标题",
    "source_name": "数据源名称",
    "total_records": 100,           // 总记录数
    "filtered_records": 50,         // 过滤后记录数
    "returned_records": 10,         // 实际返回记录数
    "field_mapping": {              // 中英文字段对照
        "id": "序号",
        "enterprise_name": "企业名称"
    },
    "data": [...],                  // 实际数据
    "query_time": "2024-01-01T12:00:00"
}
```

## 📁 数据源格式

每个 `.md` 数据源文件应包含以下结构：

```markdown
-- 数据源标题

[
    {
        "id": 1,
        "enterprise_name": "企业名称",
        "field1": "值1",
        "field2": "值2"
    }
]

中文字段    英文字段
序号       id
企业名称    enterprise_name
字段1      field1
字段2      field2
```

## 🚀 快速开始

1. **启动服务器**:
   ```bash
   ./scripts/run_server.sh
   ```

2. **测试功能**:
   ```bash
   ./scripts/run_tests.sh
   ```

3. **与 Claude Desktop 集成**:
   - 修改配置文件中的路径为你的项目路径

## 🔧 扩展数据源

要添加新的数据源，只需：

1. 在 `data` 目录下创建新的 `.md` 文件
2. 按照指定格式添加数据和字段映射
3. 重启服务器，系统会自动创建对应的查询工具


